{"version": 3, "file": "admin.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,I,mBCAlF,MAAM,EAA+BI,OAAOC,KAAKC,OAAO,a,aCAxD,SAASC,EAAgBC,EAAGC,GAC1B,OAAOF,EAAkBZ,OAAOe,eAAiBf,OAAOe,eAAeC,OAAS,SAAUH,EAAGC,GAC3F,OAAOD,EAAEI,UAAYH,EAAGD,CAC1B,EAAGD,EAAgBC,EAAGC,EACxB,CCHA,SAASI,EAAeL,EAAGd,GACzBc,EAAEP,UAAYN,OAAOmB,OAAOpB,EAAEO,WAAYO,EAAEP,UAAUc,YAAcP,EAAGE,EAAeF,EAAGd,EAC3F,CCHA,MAAM,EAA+BU,OAAOC,KAAKC,OAAO,kC,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,sC,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,8B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,+B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,2B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,uB,aCWnCU,EAAkB,SAAAC,GAAA,SAAAD,IAAA,QAAAE,EAAAC,EAAAC,UAAAC,OAAAC,EAAA,IAAAC,MAAAJ,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IAAAF,EAAAE,GAAAJ,UAAAI,GAMxB,OANwBN,EAAAD,EAAAd,KAAAsB,MAAAR,EAAA,OAAAS,OAAAJ,KAAA,MAIrCK,SAAU,EAAKT,EACfU,aAAO,EAAAV,EACPW,mBAAa,EAAAX,CAAA,CAAAL,EAAAG,EAAAC,GAAA,IAAAa,EAAAd,EAAAf,UAmFZ,OAnFY6B,EAEbC,OAAA,SAAOC,GACLf,EAAAhB,UAAM8B,OAAM5B,KAAC,KAAA6B,GACbC,KAAKN,SAAU,EACfM,KAAKL,QAAUK,KAAKC,MAAMN,QAC1BK,KAAKJ,cAAgBM,IAAOF,KAAKL,QAAQQ,UAAyB,2BACpE,EAACN,EAEDO,UAAA,WACE,MAAO,cACT,EAACP,EAEDQ,MAAA,WACE,OAAOC,IAAAA,WAAeC,MAAM,iDAC9B,EAACV,EAEDW,QAAA,WAA4B,IAAAC,EAAA,KAC1B,OACEC,EAAA,OAAKN,UAAU,cACbM,EAAA,OAAKN,UAAU,QACbM,EAAA,OAAKN,UAAU,aAAaO,MAAM,uBAChCD,EAAA,OAAKN,UAAU,2BACZE,IAAAA,WAAeC,MAAM,wDAGxBG,EAAA,SAAOE,UAAU,MAAMR,UAAU,cAAcS,KAAMb,KAAKJ,iBAG5Dc,EAAA,OAAKN,UAAU,aAAaO,MAAM,uBAC/BG,IAAAA,UACC,CACEH,MAAO,kBACPP,UAAW,yBACXW,SAAUf,KAAKN,QACfsB,QAAS,WACPP,EAAKQ,UACP,GAEFX,IAAAA,WAAeC,MAAM,yCACrB,IAEDO,IAAAA,UACC,CACEH,MAAO,8CACPP,UAAW,SACXW,SAAUf,KAAKN,QACfsB,QAAS,WACPP,EAAKS,MACP,GAEFZ,IAAAA,WAAeC,MAAM,6CAMjC,EAACV,EAEDoB,SAAA,WAAW,IAAAE,EAAA,KACTnB,KAAKN,SAAU,EAEf,IAAME,EAAgBI,KAAKJ,gBACrBwB,EAAQpB,KAAKL,QAAQ0B,KAE3Bf,IAAAA,QACW,CACPgB,IAAQhB,IAAAA,MAAUH,UAAU,UAAS,yBACrCoB,OAAQ,OACRC,KAAM,CAAEJ,MAAAA,EAAOxB,cAAAA,KAEhB6B,KAAK,SAACC,GACLP,EAAKD,OAELZ,IAAAA,MAAUqB,YAAYD,GAGtBE,QAAQC,IAAIvB,IAAAA,MAAUwB,QAAQ,OAAQV,IACtCV,EAAEqB,QACJ,GAAE,MACK,WACLZ,EAAKzB,SAAU,CACjB,EACJ,EAACX,CAAA,CAzFoC,CAASiD,KAA3BjD,EACZkD,+BAAgC,EADpBlD,EAEZmD,6BAA8B,ECJe,IAEjCC,EAAY,SAAAC,GAAA,SAAAD,IAAA,QAAAlD,EAAAC,EAAAC,UAAAC,OAAAC,EAAA,IAAAC,MAAAJ,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IAAAF,EAAAE,GAAAJ,UAAAI,GAChB,OADgBN,EAAAmD,EAAAlE,KAAAsB,MAAA4C,EAAA,OAAA3C,OAAAJ,KAAA,MAC/BK,SAAU,EAAKT,CAAA,CAAAL,EAAAuD,EAAAC,GAAA,IAAAvC,EAAAsC,EAAAnE,UAkEd,OAlEc6B,EAEfC,OAAA,SAAOC,GAAqC,IAAAU,EAAA,KAC1C2B,EAAApE,UAAM8B,OAAM5B,KAAC,KAAA6B,GACbC,KAAKN,SAAU,EAIfY,IAAAA,QAAY+B,KAAK,CAAC,WAAWZ,KAAK,WAChChB,EAAKf,SAAU,EACfgB,EAAEqB,QACJ,EACF,EAAClC,EAEDW,QAAA,SAAQT,GACN,GAAIC,KAAKN,QACP,OAAOgB,EAAC4B,IAAgB,MAG1B,IAAMC,EAAcC,IAElBlC,IAAAA,MAAUmC,IAAI,QAAQC,OAAO,SAACC,GAAQ,OAAMA,EAAIC,QAAQ,IAG1D,OACElC,EAAA,OAAKN,UAAU,8BAA8BO,MAAM,mCAChD4B,EAAKM,IAAI,SAAClD,GACT,IAAMmD,EAA0BnD,EAAQQ,UAAyB,2BAC3D4C,EAA0B,kBAAkBD,EAAuB,qFAEzE,OACEpC,EAAA,OAAKN,UAAU,0BACbM,EAAA,OAAKN,UAAU,8BACZ4C,IAAQrD,GACTe,EAAA,QAAMN,UAAU,0CAA0CT,EAAQsD,QAElEvC,EAAA,OAAKC,MAAM,gFACRmC,GACCpC,EAAA,OACEC,MAAOoC,EACP3C,UAAU,qBACVY,QAAS,WAAF,OAAQV,IAAAA,MAAU4C,KAAKnE,EAAoB,CAAEY,QAAAA,GAAU,KAIhEmD,GACApC,EAAA,OAAKN,UAAU,sBACZU,IAAAA,UACC,CACEH,MAAO,uDACPP,UAAW,SACXY,QAAS,WACPV,IAAAA,MAAU4C,KAAKnE,EAAoB,CAAEY,QAAAA,GACvC,GAEFW,IAAAA,WAAeC,MAAM,sDAQrC,GAGN,EAAC4B,CAAA,CAnE8B,CAASgB,KCR1C7C,IAAAA,aAAiB8C,IAAI,4BAA6B,WAChD9C,IAAAA,cAAiB,IAAK,6BAA6B+C,aAAalB,EAClE,E", "sources": ["webpack://@wusong8899/flarum-tag-background/webpack/bootstrap", "webpack://@wusong8899/flarum-tag-background/webpack/runtime/compat get default export", "webpack://@wusong8899/flarum-tag-background/webpack/runtime/define property getters", "webpack://@wusong8899/flarum-tag-background/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/flarum-tag-background/external root \"flarum.core.compat['admin/app']\"", "webpack://@wusong8899/flarum-tag-background/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/flarum-tag-background/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/flarum-tag-background/external root \"flarum.core.compat['admin/components/ExtensionPage']\"", "webpack://@wusong8899/flarum-tag-background/external root \"flarum.core.compat['common/components/Button']\"", "webpack://@wusong8899/flarum-tag-background/external root \"flarum.core.compat['common/components/LoadingIndicator']\"", "webpack://@wusong8899/flarum-tag-background/external root \"flarum.core.compat['tags/common/utils/sortTags']\"", "webpack://@wusong8899/flarum-tag-background/external root \"flarum.core.compat['tags/common/helpers/tagIcon']\"", "webpack://@wusong8899/flarum-tag-background/external root \"flarum.core.compat['common/components/Modal']\"", "webpack://@wusong8899/flarum-tag-background/external root \"flarum.core.compat['common/utils/Stream']\"", "webpack://@wusong8899/flarum-tag-background/./src/admin/components/SetBackgroundModal.tsx", "webpack://@wusong8899/flarum-tag-background/./src/admin/components/SettingsPage.tsx", "webpack://@wusong8899/flarum-tag-background/./src/admin/index.ts"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['admin/app'];", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['admin/components/ExtensionPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Button'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/LoadingIndicator'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['tags/common/utils/sortTags'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['tags/common/helpers/tagIcon'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Modal'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/utils/Stream'];", "import app from 'flarum/admin/app';\nimport Modal, { IInternalModalAttrs } from 'flarum/common/components/Modal';\nimport Button from 'flarum/common/components/Button';\nimport Stream from 'flarum/common/utils/Stream';\nimport type Mithril from 'mithril';\nimport type Tag from 'flarum/tags/common/models/Tag';\n\ninterface SetBackgroundModalAttrs extends IInternalModalAttrs {\n  tagData: Tag;\n}\n\nexport default class SetBackgroundModal extends Modal<SetBackgroundModalAttrs> {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  loading = false;\n  tagData!: Tag;\n  backgroundUrl!: Stream<string | null>;\n\n  oninit(vnode: Mithril.Vnode<SetBackgroundModalAttrs, this>) {\n    super.oninit(vnode);\n    this.loading = false;\n    this.tagData = this.attrs.tagData;\n    this.backgroundUrl = Stream(this.tagData.attribute<string | null>('wusong8899BackgroundURL'));\n  }\n\n  className(): string {\n    return 'Modal--small';\n  }\n\n  title(): Mithril.Children {\n    return app.translator.trans('wusong8899-tag-background.admin.set-background');\n  }\n\n  content(): Mithril.Children {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            <div className=\"ModuleItemSettingsLabel\">\n              {app.translator.trans('wusong8899-tag-background.admin.item-background-url')}\n            </div>\n            {/* `bidi` is a compat attr supported by Flarum's Stream */}\n            <input maxlength=\"200\" className=\"FormControl\" bidi={this.backgroundUrl as any} />\n          </div>\n\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                style: 'min-width:66px;',\n                className: 'Button Button--primary',\n                disabled: this.loading,\n                onclick: () => {\n                  this.saveData();\n                },\n              },\n              app.translator.trans('wusong8899-tag-background.admin.save')\n            )}\n            &nbsp;\n            {Button.component(\n              {\n                style: 'min-width:66px;background: rgba(0,0,0,0.1);',\n                className: 'Button',\n                disabled: this.loading,\n                onclick: () => {\n                  this.hide();\n                },\n              },\n              app.translator.trans('wusong8899-tag-background.admin.cancel')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  saveData() {\n    this.loading = true;\n\n    const backgroundUrl = this.backgroundUrl();\n    const tagID = this.tagData.id()!;\n\n    app\n      .request({\n        url: `${app.forum.attribute('apiUrl')}/tagBackgroundSetImage`,\n        method: 'POST',\n        body: { tagID, backgroundUrl },\n      })\n      .then((result: any) => {\n        this.hide();\n        // pushPayload expects a JSON:API payload; the controller returns Tag payload\n        app.store.pushPayload(result as any);\n        // Ensure store is updated; force redraw\n        // eslint-disable-next-line no-console\n        console.log(app.store.getById('tags', tagID));\n        m.redraw();\n      })\n      .catch(() => {\n        this.loading = false;\n      });\n  }\n}\n\n", "import app from 'flarum/admin/app';\nimport ExtensionPage from 'flarum/admin/components/ExtensionPage';\nimport Button from 'flarum/common/components/Button';\nimport LoadingIndicator from 'flarum/common/components/LoadingIndicator';\nimport sortTags from 'flarum/tags/common/utils/sortTags';\nimport tagIcon from 'flarum/tags/common/helpers/tagIcon';\nimport type Tag from 'flarum/tags/common/models/Tag';\nimport type Mithril from 'mithril';\n\nimport SetBackgroundModal from './SetBackgroundModal';\n\nexport default class SettingsPage extends ExtensionPage {\n  loading = false;\n\n  oninit(vnode: Mithril.Vnode<unknown, this>) {\n    super.oninit(vnode);\n    this.loading = true;\n\n    // Load tag list with parents\n    // @ts-ignore tagList is provided by flarum/tags\n    app.tagList.load(['parent']).then(() => {\n      this.loading = false;\n      m.redraw();\n    });\n  }\n\n  content(vnode: Mithril.VnodeDOM<unknown, this>): JSX.Element {\n    if (this.loading) {\n      return <LoadingIndicator />;\n    }\n\n    const tags: Tag[] = sortTags(\n      // @ts-ignore tags model provided by flarum/tags\n      app.store.all('tags').filter((tag: Tag) => !tag.parent())\n    );\n\n    return (\n      <div className=\"tagBackgroundSettingsGroups\" style=\"text-align: left;padding: 20px;\">\n        {tags.map((tagData: Tag) => {\n          const wusong8899BackgroundURL = tagData.attribute<string | null>('wusong8899BackgroundURL');\n          const tagBackgroundImageStyle = `background:url(${wusong8899BackgroundURL});background-size: cover;background-position: center;background-repeat: no-repeat;`;\n\n          return (\n            <div className=\"tagBackgroundContainer\">\n              <div className=\"tagBackgroundItemContainer\">\n                {tagIcon(tagData)}\n                <span className=\"tagBackgroundItemName TagListItem-name\">{tagData.name()}</span>\n\n                <div style=\"padding-top: 10px;display: flex;justify-content: center;align-items: center;\">\n                  {wusong8899BackgroundURL && (\n                    <div\n                      style={tagBackgroundImageStyle as any}\n                      className=\"tagBackgroundImage\"\n                      onclick={() => app.modal.show(SetBackgroundModal, { tagData })}\n                    ></div>\n                  )}\n\n                  {!wusong8899BackgroundURL && (\n                    <div className=\"tagBackgroundImage\">\n                      {Button.component(\n                        {\n                          style: 'min-width: 66px;font-size: 12px;font-weight: normal;',\n                          className: 'Button',\n                          onclick: () => {\n                            app.modal.show(SetBackgroundModal, { tagData });\n                          },\n                        },\n                        app.translator.trans('wusong8899-tag-background.admin.set-background')\n                      )}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    );\n  }\n}\n\n", "import app from 'flarum/admin/app';\nimport SettingsPage from './components/SettingsPage';\n\napp.initializers.add('wusong8899-tag-background', () => {\n  app.extensionData.for('wusong8899-tag-background').registerPage(SettingsPage);\n});\n\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "flarum", "core", "compat", "_setPrototypeOf", "t", "e", "setPrototypeOf", "bind", "__proto__", "_inherits<PERSON><PERSON>e", "create", "constructor", "SetBackgroundModal", "_Modal", "_this", "_len", "arguments", "length", "args", "Array", "_key", "apply", "concat", "loading", "tagData", "backgroundUrl", "_proto", "oninit", "vnode", "this", "attrs", "Stream", "attribute", "className", "title", "app", "trans", "content", "_this2", "m", "style", "maxlength", "bidi", "<PERSON><PERSON>", "disabled", "onclick", "saveData", "hide", "_this3", "tagID", "id", "url", "method", "body", "then", "result", "pushPayload", "console", "log", "getById", "redraw", "Modal", "isDismissibleViaBackdropClick", "isDismissibleViaCloseButton", "SettingsPage", "_ExtensionPage", "load", "LoadingIndicator", "tags", "sortTags", "all", "filter", "tag", "parent", "map", "wusong8899BackgroundURL", "tagBackgroundImageStyle", "tagIcon", "name", "show", "ExtensionPage", "add", "registerPage"], "sourceRoot": ""}