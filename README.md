# Daily Check In 每日签到

![License](https://img.shields.io/badge/license-MIT-blue.svg)

A [Flarum](http://flarum.org) extension. Add a button in the index page that allow user to daily check in the forum and receive reward.  
一个 Flarum 扩展。在论坛首页加入签到按钮，允许用户进行每日签到并获得奖励。

### Installation

Install with composer:

```sh
composer require wusong8899/flarum-daily-check-in
```

### Updating

```sh
composer update wusong8899/flarum-daily-check-in
php flarum migrate
php flarum cache:clear
```

### Links

- [Github](https://github.com/wusong8899/flarum-daily-check-in)
- [Packagist](https://packagist.org/packages/wusong8899/flarum-daily-check-in)

### ScreenShot

![image](https://user-images.githubusercontent.com/29644610/191722290-9a54b9c6-664d-4e82-9181-9a9a47ad476d.jpg)
![image](https://user-images.githubusercontent.com/29644610/191472984-5724b8ba-38cd-40cc-96ee-7d1ae94ab45c.jpg)
